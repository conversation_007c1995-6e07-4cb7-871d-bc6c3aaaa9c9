package jnpf.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @CLASSNAME HzFileEnity
 * <AUTHOR>
 * @Date 2025/8/12、17:50
 */
@Data
@TableName("hz_file")
public class HzFileEnity {
    @TableId("f_id")
    private String id;
    @TableField("f_file_name")
    private String fileName;
    @TableField("f_file_path")
    private String filePath;
    @TableField("f_level")
    private Integer level;
    @TableField("f_pid")
    private String pid;
    @TableField("f_task_id")
    private String taskId;
    @TableField("f_create_time")
    private String createTime;
    @TableField("f_user_id")
    private String userId;
    @TableField("f_project_id")
    private String projectId;
    @TableField("f_result_id")
    private String resultId;
    @TableField("f_type")
    private Integer type;
    @TableField("f_file_url")
    private String fileUrl;
}

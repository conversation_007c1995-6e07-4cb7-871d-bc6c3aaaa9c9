package jnpf.database.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SQL日志配置类
 * 用于控制SQL日志的输出行为
 */
@Data
@Component
@ConfigurationProperties(prefix = "jnpf.sql.log")
public class SqlLogConfig {

    /**
     * 是否启用SQL日志输出到控制台
     * 默认为true，在生产环境建议设置为false
     */
    private boolean enabled = true;

    /**
     * 是否显示SQL执行时间
     * 默认为true
     */
    private boolean showExecuteTime = true;

    /**
     * 是否显示完整的SQL语句（包含参数值）
     * 默认为true
     */
    private boolean showCompleteSql = true;

    /**
     * 是否显示参数列表
     * 默认为true
     */
    private boolean showParameters = true;

    /**
     * 是否显示Mapper信息
     * 默认为true
     */
    private boolean showMapper = true;

    /**
     * SQL执行时间超过多少毫秒时才输出（慢SQL检测）
     * 默认为0，表示所有SQL都输出
     * 设置为大于0的值时，只有执行时间超过该值的SQL才会输出
     */
    private long slowSqlThreshold = 0;
}

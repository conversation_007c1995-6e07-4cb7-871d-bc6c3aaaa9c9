package jnpf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jnpf.entity.HzProjectFruitEntity;
import java.util.List;

/**
 * 项目成果信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
public interface HzProjectFruitService extends IService<HzProjectFruitEntity> {


    /**
     * 信息
     *
     * @param id 主键值
     * @return HzProjectFruitEntity
     */
    HzProjectFruitEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 实体对象
     */
    void delete(HzProjectFruitEntity entity);


    /**
     * 更新
     *
     * @param id     主键值
     * @param entity 实体对象
     * @return 是否成功
     */
    boolean update(String id, HzProjectFruitEntity entity);

    /**
     * 验证成果编号是否存在
     *
     * @param fruitCode 成果编号
     * @param id        主键值
     * @return 是否存在
     */
    boolean isExistByFruitCode(String fruitCode, String id);

    /**
     * 根据项目编号获取成果列表
     *
     * @param projectCode 项目编号
     * @return List<HzProjectFruitEntity>
     */
    List<HzProjectFruitEntity> getListByProjectCode(String projectCode);

    /**
     * 根据任务编号获取成果列表
     *
     * @param taskCode 任务编号
     * @return List<HzProjectFruitEntity>
     */
    List<HzProjectFruitEntity> getListByTaskCode(String taskCode);

}

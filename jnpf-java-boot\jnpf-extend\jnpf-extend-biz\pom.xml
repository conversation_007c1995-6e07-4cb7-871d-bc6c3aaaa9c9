<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jnpf-extend</artifactId>
        <groupId>com.jnpf</groupId>
        <version>5.2.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jnpf-extend-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-extend-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-system-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- PDF -->
        <dependency>
            <groupId>com.github.librepdf</groupId>
            <artifactId>openpdf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.librepdf</groupId>
            <artifactId>openpdf-fonts-extra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-generater-base</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>

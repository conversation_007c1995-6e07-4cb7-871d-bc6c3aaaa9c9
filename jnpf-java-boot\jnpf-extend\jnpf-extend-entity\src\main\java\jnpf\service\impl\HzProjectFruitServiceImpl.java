package jnpf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jnpf.entity.HzProjectFruitEntity;
import jnpf.mapper.HzProjectFruitMapper;
import jnpf.service.HzProjectFruitService;
import jnpf.util.StringUtil;
import jnpf.util.UserProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 项目成果信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
@Service
public class HzProjectFruitServiceImpl extends ServiceImpl<HzProjectFruitMapper, HzProjectFruitEntity> implements HzProjectFruitService {

    @Autowired
    private UserProvider userProvider;

    @Override
    public HzProjectFruitEntity getInfo(String id) {
        QueryWrapper<HzProjectFruitEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_id", id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void delete(HzProjectFruitEntity entity) {
        if (entity != null) {
            this.updateById(entity);
        }
    }

    @Override
    public boolean update(String id, HzProjectFruitEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public boolean isExistByFruitCode(String fruitCode, String id) {
        QueryWrapper<HzProjectFruitEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_fruit_code", fruitCode);
        if (StringUtil.isNotEmpty(id)) {
            queryWrapper.ne("f_id", id);
        }
        return this.count(queryWrapper) > 0;
    }

    @Override
    public List<HzProjectFruitEntity> getListByProjectCode(String projectCode) {
        QueryWrapper<HzProjectFruitEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_project_code", projectCode);
        queryWrapper.orderByDesc("f_creator_time");
        return this.list(queryWrapper);
    }

    @Override
    public List<HzProjectFruitEntity> getListByTaskCode(String taskCode) {
        QueryWrapper<HzProjectFruitEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_task_code", taskCode);
        queryWrapper.orderByDesc("f_creator_time");
        return this.list(queryWrapper);
    }

}

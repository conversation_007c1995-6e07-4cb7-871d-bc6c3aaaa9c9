package jnpf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jnpf.base.service.SuperServiceImpl;
import jnpf.entity.HzProjectTaskEntity;
import jnpf.mapper.HzProjectTaskMapper;
import jnpf.service.HzProjectTaskService;
import jnpf.util.DateUtil;
import jnpf.util.StringUtil;
import jnpf.util.UserProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 项目任务信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
@Service
public class HzProjectTaskServiceImpl extends SuperServiceImpl<HzProjectTaskMapper, HzProjectTaskEntity> implements HzProjectTaskService {

    @Autowired
    private UserProvider userProvider;

    @Override
    public HzProjectTaskEntity getInfo(String id) {
        QueryWrapper<HzProjectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_id", id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void delete(HzProjectTaskEntity entity) {
        if (entity != null) {
            this.updateById(entity);
        }
    }


    @Override
    public boolean update(String id, HzProjectTaskEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public boolean isExistByTaskCode(String taskCode, String id) {
        QueryWrapper<HzProjectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_task_code", taskCode);
        if (StringUtil.isNotEmpty(id)) {
            queryWrapper.ne("f_id", id);
        }
        return this.count(queryWrapper) > 0;
    }

    @Override
    public List<HzProjectTaskEntity> getListByProjectCode(String projectCode) {
        QueryWrapper<HzProjectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("f_project_code", projectCode);
        queryWrapper.eq("f_delete_mark", 0);
        queryWrapper.orderByAsc("f_task_code");
        return this.list(queryWrapper);
    }

}

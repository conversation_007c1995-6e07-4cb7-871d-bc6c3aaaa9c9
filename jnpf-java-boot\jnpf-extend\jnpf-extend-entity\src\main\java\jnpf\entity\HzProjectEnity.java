package jnpf.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.base.entity.SuperExtendEntity;
import jnpf.constant.TableFieldsNameConst;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
@Data
@TableName("hz_project")
public class HzProjectEnity   {
    /**
     * 主键
     */
    @TableId(TableFieldsNameConst.F_ID)
    public String id;
    /**
     * 项目编号
     */
    @TableField("f_project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField("f_project_name")
    private String projectName;

    /**
     * 项目地址
     */
    @TableField("f_project_address")
    private String projectAddress;

    /**
     * 业务归属
     */
    @TableField("f_business_belong")
    private String businessBelong;

    /**
     * 项目承接人
     */
    @TableField("f_project_leader_id")
    private String projectLeaderId;

    /**
     * 紧急等级
     */
    @TableField("f_urgent_level")
    private String urgentLevel;

    /**
     * 保密等级
     */
    @TableField("f_secret_level")
    private String secretLevel;

    /**
     * 项目类型
     */
    @TableField("f_project_type_id")
    private String projectTypeId;

    /**
     * 服务内容
     */
    @TableField("f_service_content")
    private String serviceContent;

    /**
     * 建筑类别
     */
    @TableField("f_building_category_ids")
    private String buildingCategoryIds;

    /**
     * 建筑性质
     */
    @TableField("f_building_nature")
    private String buildingNature;

    /**
     * 专业类别
     */
    @TableField("f_professional_category_ids")
    private String professionalCategoryIds;

    /**
     * 总投资金额
     */
    @TableField("f_total_investment")
    private BigDecimal totalInvestment;

    /**
     * 建安投资金额
     */
    @TableField("f_construction_investment")
    private BigDecimal constructionInvestment;

    /**
     * 建设资金来源
     */
    @TableField("f_fund_source")
    private String fundSource;

    /**
     * 占地面积
     */
    @TableField("f_land_area")
    private String landArea;

    /**
     * 建筑面积
     */
    @TableField("f_building_area")
    private String buildingArea;

    /**
     * 项目计划开始时间
     */
    @TableField("f_plan_start_time")
    private Date planStartTime;

    /**
     * 项目计划结束时间
     */
    @TableField("f_plan_end_time")
    private Date planEndTime;

    /**
     * 建设单位名称
     */
    @TableField("f_construction_unit")
    private String constructionUnit;

    /**
     * 委托方特殊需求或其他情况说明
     */
    @TableField("f_special_requirements")
    private String specialRequirements;

    /**
     * 项目经理
     */
    @TableField("f_project_manager_id")
    private String projectManagerId;

    /**
     * 执行经理
     */
    @TableField("f_execution_manager_id")
    private String executionManagerId;

    /**
     * 项目成员
     */
    @TableField("f_project_member_ids")
    private String projectMemberIds;

    /**
     * 项目状态
     */
    @TableField("f_project_status")
    private String projectStatus;

    /**
     * 建设单位联系人
     */
    @TableField("f_construction_person")
    private String constructionPerson;

    /**
     * 咨询费计取依据文件
     */
    @TableField("f_contract_refer_name")
    private String contractReferName;

    /**
     * 下浮率
     */
    @TableField("f_contract_down_percent")
    private String contractDownPercent;

    /**
     * 合同金额
     */
    @TableField("f_contract_amount")
    private BigDecimal contractAmount;

    /**
     * 确定方式
     */
    @TableField("f_contract_confirm_method")
    private String contractConfirmMethod;

    /**
     * 付款方式
     */
    @TableField("f_contract_payment")
    private String contractPayment;

    /**
     * 计价方式
     */
    @TableField("f_contract_count_method")
    private String contractCountMethod;

    /**
     * 合同开始时间
     */
    @TableField("f_contract_start_time")
    private Date contractStartTime;

    /**
     * 合同结束时间
     */
    @TableField("f_contract_end_time")
    private Date contractEndTime;

    /**
     * 合同文件
     */
    @TableField("f_contract_files")
    private String contractFiles;

    /**
     * 合同备注说明
     */
    @TableField("f_contract_note")
    private String contractNote;

    /**
     * 流程id
     */
    @TableField("f_flow_id")
    private String flowId;

    /**
     * 流程任务主键
     */
    @TableField("f_flow_task_id")
    private String flowTaskId;

    /**
     * 项目收益
     */
    @TableField("f_allocation_amount")
    private String allocationAmount;

    /**
     * 相关部门
     */
    @TableField("f_departments")
    private String departments;

}

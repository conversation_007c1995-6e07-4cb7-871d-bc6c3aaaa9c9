<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/jnpf-java-boot/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-common/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-workflow/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-datareport/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-scheduletask/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-workflow-core/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-file-core-starter/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-java-datareport-univer/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-java-datareport-univer-core/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-scheduletask/xxl-job-admin/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-java-boot/jnpf-workflow/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-common/jnpf-boot-common/jnpf-common-compatible/jnpf-common-office-v3/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-java-tenant/pom.xml" />
        <option value="$PROJECT_DIR$/jnpf-file-preview/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="temurin-21" project-jdk-type="JavaSDK" />
</project>
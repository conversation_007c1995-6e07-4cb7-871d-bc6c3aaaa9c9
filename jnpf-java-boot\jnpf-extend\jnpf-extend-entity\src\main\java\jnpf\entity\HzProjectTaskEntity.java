package jnpf.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.base.entity.SuperExtendEntity;
import jnpf.constant.TableFieldsNameConst;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目任务信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
@Data
@TableName("hz_project_task")
public class HzProjectTaskEntity  {

    /**
     * 主键
     */
    @TableId(TableFieldsNameConst.F_ID)
    public String id;
    /**
     * 项目编号
     */
    @TableField("f_project_code")
    private String projectCode;

    /**
     * 任务编号
     */
    @TableField("f_task_code")
    private String taskCode;

    /**
     * 标准化任务
     */
    @TableField("f_standard_task_id")
    private String standardTaskId;

    /**
     * 任务名称
     */
    @TableField("f_custom_task_name")
    private String customTaskName;

    /**
     * 任务人员
     */
    @TableField("f_task_user_id")
    private String taskUserId;

    /**
     * 优先级
     */
    @TableField("f_priority")
    private String priority;

    /**
     * 专业类别
     */
    @TableField("f_professional_category_id")
    private String professionalCategoryId;

    /**
     * 任务工程
     */
    @TableField("f_overview_ids")
    private String overviewIds;

    /**
     * 计划开始日期
     */
    @TableField("f_plan_start_date")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @TableField("f_plan_end_date")
    private Date planEndDate;

    /**
     * 任务累计工时
     */
    @TableField("f_total_hours")
    private BigDecimal totalHours;

    /**
     * 任务进度
     */
    @TableField("f_progress")
    private Integer progress;

    /**
     * 备注
     */
    @TableField("f_remark")
    private String remark;

    /**
     * 任务状态
     */
    @TableField("f_task_status")
    private String taskStatus;

    /**
     * 复核状态
     */
    @TableField("f_review_status")
    private String reviewStatus;

    /**
     * 流程id
     */
    @TableField("f_flow_id")
    private String flowId;

    /**
     * 流程任务主键
     */
    @TableField("f_flow_task_id")
    private String flowTaskId;

    /**
     * 复核级别
     */
    @TableField("f_review_level")
    private String reviewLevel;

    /**
     * 二级复核评分
     */
    @TableField("f_score_level_2")
    private String scoreLevel2;

    /**
     * 三级复核评分
     */
    @TableField("f_score_level_3")
    private String scoreLevel3;

    /**
     * 二级复核评语
     */
    @TableField("f_note_level_2")
    private String noteLevel2;

    /**
     * 三级复核评语
     */
    @TableField("f_note_level_3")
    private String noteLevel3;

}

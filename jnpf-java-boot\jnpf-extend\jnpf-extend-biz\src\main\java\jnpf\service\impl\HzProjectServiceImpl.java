package jnpf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jnpf.base.Pagination;
import jnpf.base.service.SuperServiceImpl;
import jnpf.entity.HzProjectEnity;
import jnpf.mapper.HzProjectMapper;
import jnpf.service.HzProjectService;
import jnpf.util.RandomUtil;
import jnpf.util.StringUtil;
import jnpf.util.UserProvider;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 项目信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
@Service
public class HzProjectServiceImpl extends SuperServiceImpl<HzProjectMapper, HzProjectEnity> implements HzProjectService {

    @Override
    public List<HzProjectEnity> getList(Pagination pagination) {
        QueryWrapper<HzProjectEnity> queryWrapper = new QueryWrapper<>();
        if (StringUtil.isNotEmpty(pagination.getKeyword())) {
            queryWrapper.lambda().and(
                    t -> t.like(HzProjectEnity::getProjectCode, pagination.getKeyword())
                            .or().like(HzProjectEnity::getProjectName, pagination.getKeyword())
                            .or().like(HzProjectEnity::getConstructionUnit, pagination.getKeyword())
            );
        }
        // 按创建时间倒序排列

        Page<HzProjectEnity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<HzProjectEnity> projectPage = this.page(page, queryWrapper);
        return pagination.setData(projectPage.getRecords(), projectPage.getTotal());
    }

    @Override
    public HzProjectEnity getInfo(String id) {
        QueryWrapper<HzProjectEnity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HzProjectEnity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(HzProjectEnity entity) {
        entity.setId(RandomUtil.uuId());
        // 设置默认项目状态为0（待开始）
        if (StringUtil.isEmpty(entity.getProjectStatus())) {
            entity.setProjectStatus("0");
        }
        this.save(entity);
    }

    @Override
    public boolean update(String id, HzProjectEnity entity) {
        entity.setId(id);;
        return this.updateById(entity);
    }

    @Override
    public void delete(HzProjectEnity entity) {
        if (entity != null) {
            // 软删
            this.updateById(entity);
        }
    }

    @Override
    public boolean isExistByProjectCode(String projectCode, String id) {
        QueryWrapper<HzProjectEnity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HzProjectEnity::getProjectCode, projectCode);
        if (StringUtil.isNotEmpty(id)) {
            queryWrapper.lambda().ne(HzProjectEnity::getId, id);
        }
        // 只查询未删除的记录
        return this.count(queryWrapper) > 0;
    }

}

package jnpf.service;

import jnpf.base.Pagination;
import jnpf.base.service.SuperService;
import jnpf.entity.HzProjectEnity;

import java.util.List;

/**
 * 项目信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
public interface HzProjectService extends SuperService<HzProjectEnity> {

    /**
     * 获取项目列表
     *
     * @param pagination 分页参数
     * @return 项目列表
     */
    List<HzProjectEnity> getList(Pagination pagination);

    /**
     * 获取项目信息
     *
     * @param id 主键
     * @return 项目信息
     */
    HzProjectEnity getInfo(String id);

    /**
     * 删除项目
     *
     * @param entity 项目实体
     */
    void delete(HzProjectEnity entity);

    /**
     * 创建项目
     *
     * @param entity 项目实体
     */
    void create(HzProjectEnity entity);

    /**
     * 更新项目
     *
     * @param id 主键
     * @param entity 项目实体
     * @return 是否成功
     */
    boolean update(String id, HzProjectEnity entity);

    /**
     * 验证项目编号是否存在
     *
     * @param projectCode 项目编号
     * @param id 主键（排除自身）
     * @return 是否存在
     */
    boolean isExistByProjectCode(String projectCode, String id);

}
package jnpf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jnpf.base.service.SuperService;
import jnpf.entity.HzProjectTaskEntity;

import java.util.List;

/**
 * 项目任务信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
public interface HzProjectTaskService extends SuperService<HzProjectTaskEntity> {



    /**
     * 信息
     *
     * @param id 主键值
     * @return
     */
    HzProjectTaskEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 实体对象
     */
    void delete(HzProjectTaskEntity entity);

    /**
     * 更新
     *
     * @param id     主键值
     * @param entity 实体对象
     * @return
     */
    boolean update(String id, HzProjectTaskEntity entity);

    /**
     * 验证任务编号是否存在
     *
     * @param taskCode 任务编号
     * @param id       主键值
     * @return
     */
    boolean isExistByTaskCode(String taskCode, String id);

    /**
     * 根据项目编号获取任务列表
     *
     * @param projectCode 项目编号
     * @return
     */
    List<HzProjectTaskEntity> getListByProjectCode(String projectCode);

}

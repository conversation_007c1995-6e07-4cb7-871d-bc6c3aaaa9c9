package jnpf.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.base.entity.SuperExtendEntity;
import jnpf.constant.TableFieldsNameConst;
import lombok.Data;

import java.util.Date;

/**
 * 项目成果信息表
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2025-01-14
 */
@Data
@TableName("hz_project_fruit")
public class HzProjectFruitEntity  {
    /**
     * 主键
     */
    @TableId(TableFieldsNameConst.F_ID)
    public String id;
    /**
     * 项目编号
     */
    @TableField("f_project_code")
    private String projectCode;

    /**
     * 任务编号
     */
    @TableField("f_task_code")
    private String taskCode;

    /**
     * 成果编号
     */
    @TableField("f_fruit_code")
    private String fruitCode;

    /**
     * 成果名称
     */
    @TableField("f_name")
    private String name;

    /**
     * 标准化任务
     */
    @TableField("f_standard_task_id")
    private String standardTaskId;

    /**
     * 标准化成果
     */
    @TableField("f_standard_fruit_id")
    private String standardFruitId;

    /**
     * 成果文件
     */
    @TableField("f_file_url")
    private String fileUrl;

    /**
     * 备注
     */
    @TableField("f_remark")
    private String remark;

    /**
     * 流程id
     */
    @TableField("f_flow_id")
    private String flowId;

    /**
     * 流程任务主键
     */
    @TableField("f_flow_task_id")
    private String flowTaskId;

}

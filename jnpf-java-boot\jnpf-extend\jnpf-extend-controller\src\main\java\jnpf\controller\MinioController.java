package jnpf.controller;

import cn.xuyanwu.spring.file.storage.FileInfo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.minio.*;
import com.alibaba.fastjson.JSON;
import io.minio.messages.Item;
import jakarta.servlet.http.HttpServletResponse;
import jnpf.base.ActionResult;
import jnpf.base.UserInfo;
import jnpf.config.ConfigValueUtil;
import jnpf.entity.HzFileEnity;
import jnpf.entity.HzProjectEnity;
import jnpf.entity.HzProjectFruitEntity;
import jnpf.entity.HzProjectTaskEntity;
import jnpf.mapper.HzFileMapper;
import jnpf.model.UpFile;
import jnpf.model.PathTypeModel;
import jnpf.model.UploaderVO;
import jnpf.service.HzProjectFruitService;
import jnpf.service.HzProjectService;
import jnpf.service.HzProjectTaskService;
import jnpf.util.*;

import java.io.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @CLASSNAME MinioController
 * <AUTHOR>
 * @Date 2025/8/8、13:49
 */


@Slf4j
@RestController
@RequestMapping("/api/minio")
public class MinioController {
        @Autowired
        private MinioClient minioClient;
    @Autowired
    private ConfigValueUtil configValueUtil;
        @Autowired
    private HzFileMapper hzFileMapper;
        @Autowired
        private UserProvider userProvider;

    @Autowired
    private HzProjectFruitService hzProjectFruitService;

    @Autowired
    private HzProjectService hzProjectService;

    @Autowired
    private HzProjectTaskService hzProjectTaskService;


        private static final String MINIO_BUCKET = "mybucket";

        @GetMapping("/list")
        public List<Object> list(ModelMap map) throws Exception {
            ListObjectsArgs args = ListObjectsArgs.builder().bucket(MINIO_BUCKET).build();
            Iterable<Result<Item>> myObjects = minioClient.listObjects(args);
            Iterator<Result<Item>> iterator = myObjects.iterator();
            List<Object> items = new ArrayList<>();
            String format = "{'fileName':'%s','fileSize':'%s'}";
            while (iterator.hasNext()) {
                Item item = iterator.next().get();
                items.add(JSON.parse(String.format(format, item.objectName(), formatFileSize(item.size()))));
            }
            return items;
        }

        @GetMapping("/fileList")
        public ActionResult fileList(HzFileEnity hzFileEnity) throws Exception {
            UserInfo userInfo = userProvider.get();
            hzFileEnity.setUserId(userInfo.getUserId());
            if(hzFileEnity.getLevel() == 1){
                
            }
            return ActionResult.success(hzFileMapper.selectList(new QueryWrapper<>(hzFileEnity)));
        }

        @PostMapping("/upload")
        public ActionResult upload(@Param("path") String path) {
            List<MultipartFile> file = UpUtil.getFileAll();
            HzFileEnity hzFileEnity = new HzFileEnity();
            String[] parts = path.split("/");
            List<String> orgfileNameList = new ArrayList<>(file.size());
            if (file == null || file.size() == 0) {

                    hzFileEnity.setFileName(parts[parts.length-1]);
                    hzFileEnity.setFilePath(path );
                    hzFileEnity.setType(0);
            }
            else {
                StringBuilder pathBuilder = new StringBuilder(path);
                for (MultipartFile multipartFile : file) {
                    String orgfileName = multipartFile.getOriginalFilename();
                    orgfileNameList.add(pathBuilder + orgfileName);
                    hzFileEnity.setFileName(multipartFile.getOriginalFilename());
                    hzFileEnity.setFilePath(pathBuilder + multipartFile.getOriginalFilename());
                    hzFileEnity.setType(1);
                    pathBuilder.append(multipartFile.getOriginalFilename());
                    try {
                        PathTypeModel pathTypeModel = new PathTypeModel();
                        pathTypeModel.setPathType("selfPath");
                        pathTypeModel.setTimeFormat("YYYY");
                        pathTypeModel.setSortRule("1");
                        pathTypeModel.setFolder("");
                        UploaderVO uploaderVO =    uploaderVO(multipartFile, "annex", pathTypeModel);
                        UpFile upFile = new UpFile();
                        upFile.setFileId(uploaderVO.getName());
                        upFile.setFileExtension(uploaderVO.getFileExtension());
                        upFile.setFileSize(uploaderVO.getFileSize());
                        upFile.setName(multipartFile.getOriginalFilename());
                        upFile.setUrl(uploaderVO.getUrl());
                        hzFileEnity.setFileUrl(JSONObject.toJSONString(upFile));
                        QueryWrapper<HzFileEnity> queryWrapper1 = new QueryWrapper<>();
                            queryWrapper1.eq("f_file_path", path);
                            List<HzFileEnity> hzFileEnityList = hzFileMapper.selectList(queryWrapper1);
                            if (!hzFileEnityList.isEmpty()) {
                                String resultId = hzFileEnityList.get(0).getResultId();
                                HzProjectFruitEntity hzProjectFruitEntity = hzProjectFruitService.getInfo(resultId);
                                List<UpFile> upFiles = JSONObject.parseArray(hzProjectFruitEntity.getFileUrl(), UpFile.class);
                                upFiles.add(upFile);
                                hzProjectFruitEntity.setFileUrl(JSONObject.toJSONString(upFiles));
                                hzProjectFruitService.update(resultId, hzProjectFruitEntity);
                            }


                    } catch (Exception e) {
                        log.error(e.getMessage());
                        return ActionResult.fail("上传失败");
                    }
                }
                path = pathBuilder.toString();
            }
            String[] paths = path.split("/");
            Integer level = paths.length;
            int lastSlashIndex = path.lastIndexOf('/');

            // 如果路径以 '/' 结尾，去掉最后一个 '/'
            if (lastSlashIndex == path.length() - 1) {
                path = path.substring(0, lastSlashIndex);
            }

            // 再次找到最后一个 '/' 的位置
            lastSlashIndex = path.lastIndexOf('/');

            // 提取从开头到该位置的字符串
            String parentFolder = path.substring(0, lastSlashIndex + 1);
            hzFileEnity.setLevel(level);
            QueryWrapper<HzFileEnity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("f_file_path", parentFolder);
            List<HzFileEnity> hzFileEnityList = hzFileMapper.selectList(queryWrapper);
            String pid = "0";
            if (!hzFileEnityList.isEmpty()) {
                pid = hzFileEnityList.get(0).getId();
                hzFileEnity.setResultId(hzFileEnityList.get(0).getResultId());
                hzFileEnity.setProjectId(hzFileEnityList.get(0).getProjectId());
                hzFileEnity.setTaskId(hzFileEnityList.get(0).getTaskId());
            }
            hzFileEnity.setPid(pid);

            hzFileMapper.insert(hzFileEnity);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("bucketName", MINIO_BUCKET);
            data.put("fileName", orgfileNameList);
            return ActionResult.success(data);
        }
    @PostMapping("/mkdir")
    public ActionResult mkdir(String path) {
            try{
                minioClient.putObject(
                        PutObjectArgs.builder().bucket(MINIO_BUCKET).object(path).stream(
                                        new ByteArrayInputStream(new byte[]{}), 0, -1)
                                .build());
                return ActionResult.success("上传成功");
            }catch (Exception e){
                return ActionResult.fail("上传失败");
            }

    }
    private UploaderVO uploaderVO(MultipartFile file, String type, PathTypeModel pathTypeModel) throws IOException {
        String orgFileName = file.getOriginalFilename();
        String fileType = UpUtil.getFileType(file);
//        if (OptimizeUtil.fileSize(file.getSize(), 1024000)) {
//            return ActionResult.fail("上传失败，文件大小超过1M");
//        }
//        if ("mail".equals(type)) {
//            type = "temporary";
//        }
        //实际文件名
        String fileName = DateUtil.dateNow("yyyyMMdd") + "_" + RandomUtil.uuId() + "." + fileType;
        //文件上传路径
        String filePath = FilePathUtil.getFilePath(type.toLowerCase());

        //文件自定义路径相对路径
        String relativeFilePath = "";
        if ("selfPath".equals(pathTypeModel.getPathType()) && pathTypeModel.getSortRule() != null) {
            // 按路径规则顺序构建生成目录
            String sortRule = pathTypeModel.getSortRule();
            List<String> rules = null;
            if (sortRule.contains("[")) {
                rules = JsonUtil.getJsonToList(sortRule, String.class);
            } else {
                rules = Arrays.asList(pathTypeModel.getSortRule().split(","));
            }
            for (String rule : rules) {
                // 按用户存储
                if ("1".equals(rule)) {
                    UserInfo userInfo = UserProvider.getUser();
                    relativeFilePath += userInfo.getUserAccount() + "/";
                }
                // 按照时间格式
                else if (StringUtil.isNotEmpty(pathTypeModel.getTimeFormat()) && "2".equals(rule)) {
                    String timeFormat = pathTypeModel.getTimeFormat();
                    timeFormat = timeFormat.replaceAll("YYYY", "yyyy");
                    timeFormat = timeFormat.replaceAll("DD", "dd");
                    LocalDate currentDate = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(timeFormat);
                    String currentDateStr = currentDate.format(formatter);
                    relativeFilePath += currentDateStr;
                    if (!currentDateStr.endsWith("/")) {
                        relativeFilePath += "/";
                    }
                }
                // 按自定义目录
                else if (StringUtil.isNotEmpty(pathTypeModel.getFolder()) && "3".equals(rule)) {
                    String folder = pathTypeModel.getFolder();
                    folder = folder.replaceAll("\\\\", "/");
                    relativeFilePath += folder;
                    if (!folder.endsWith("/")) {
                        relativeFilePath += "/";
                    }
                }
            }

            if (StringUtil.isNotEmpty(relativeFilePath)) {
                relativeFilePath = StringUtil.replaceMoreStrToOneStr(relativeFilePath, "/");
                if (relativeFilePath.startsWith("/")) {
                    relativeFilePath = relativeFilePath.substring(1);
                }
                filePath += relativeFilePath;
                fileName = relativeFilePath.replaceAll("/", ",") + fileName;
            }
        }

        UploaderVO vo = UploaderVO.builder().fileSize(file.getSize()).fileExtension(fileType).build();
        FileInfo fileInfo = FileUploadUtils.uploadFile(file, filePath, fileName);
        fileName = fileInfo.getFilename();
        String thFilename = fileInfo.getThFilename();
        if (!StringUtil.isNotEmpty(thFilename)) {
            //小图没有压缩直接用原图
            thFilename = fileName;
        }
        //自定义文件实际文件名
        if (StringUtil.isNotEmpty(relativeFilePath)) {
            fileName = relativeFilePath.replaceAll("/", ",") + fileName;
            thFilename = relativeFilePath.replaceAll("/", ",") + thFilename;
        }
        vo.setName(fileName);
//        UploadUtil.uploadFile(configValueUtil.getFileType(), type, fileName, file, filePath);
        if ("useravatar".equalsIgnoreCase(type)) {
            vo.setUrl(UploaderUtil.uploaderImg(fileName));
            vo.setUrl(UploaderUtil.uploaderImg(thFilename));
        } else if ("annex".equalsIgnoreCase(type)) {
//            UserInfo userInfo = UserProvider.getUser();
//            vo.setUrl(UploaderUtil.uploaderFile(userInfo.getId() + "#" + fileName + "#" + type));
            vo.setUrl(UploaderUtil.uploaderImg("/api/file/Image/annex/", fileName));
            vo.setThumbUrl(UploaderUtil.uploaderImg("/api/file/Image/annex/", thFilename));
        } else if ("annexpic".equalsIgnoreCase(type)) {
            vo.setUrl(UploaderUtil.uploaderImg("/api/file/Image/annexpic/", fileName));
            vo.setThumbUrl(UploaderUtil.uploaderImg("/api/file/Image/annexpic/", thFilename));
        } else {
            vo.setUrl(UploaderUtil.uploaderImg("/api/file/Image/" + type.toLowerCase() + "/", fileName));
            vo.setThumbUrl(UploaderUtil.uploaderImg("/api/file/Image/" + type.toLowerCase() + "/", thFilename));
        }

        return vo;
    }

        @RequestMapping("/download")
        public void  download(HttpServletResponse response, String fileName, String path) {
            InputStream is = null;
            try {
                is    =minioClient.getObject(
                            GetObjectArgs.builder()
                                    .bucket(MINIO_BUCKET)
                                    .object(path+fileName)
                                    .build());
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                IOUtils.copy(is, response.getOutputStream());

            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                        log.error(e.getMessage());
                    }
                }
            }
        }

        @DeleteMapping("/delete")
        public ActionResult delete(@Param("filePath")  String filePath) {
            ActionResult res = new ActionResult();
            res.setCode(200);
            try {
                QueryWrapper<HzFileEnity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("f_file_path", filePath);
                HzFileEnity hzFileEnity = hzFileMapper.selectOne(queryWrapper);
                if(hzFileEnity!=null && hzFileEnity.getLevel()==4) {
                    hzFileMapper.deleteById(hzFileEnity.getId());
//                QueryWrapper<HzFileEnity> queryWrapper1 = new QueryWrapper<>();
//                queryWrapper1.eq("f_pid", hzFileEnity.getId());
//                hzFileMapper.delete(queryWrapper1);
                    HzProjectFruitEntity hzProjectFruitEntity = hzProjectFruitService.getInfo(hzFileEnity.getResultId());
                    List<UpFile> hzFileEnityList = JSONObject.parseArray(hzProjectFruitEntity.getFileUrl(), UpFile.class);
                    hzFileEnityList.removeIf(file -> file.getName().equals(hzFileEnity.getFileName()));
                    hzProjectFruitEntity.setFileUrl(JSONObject.toJSONString(hzFileEnityList));
                    hzProjectFruitService.update(hzProjectFruitEntity.getId(), hzProjectFruitEntity);
                }
                res.setMsg("删除成功");
            } catch (Exception e) {
                res.setCode(500);
                log.error(e.getMessage());
            }
            return res;
        }

        private static String formatFileSize(long fileS) {
            DecimalFormat df = new DecimalFormat("#.00");
            String fileSizeString = "";
            String wrongSize = "0B";
            if (fileS == 0) {
                return wrongSize;
            }
            if (fileS < 1024) {
                fileSizeString = df.format((double) fileS) + " B";
            } else if (fileS < 1048576) {
                fileSizeString = df.format((double) fileS / 1024) + " KB";
            } else if (fileS < 1073741824) {
                fileSizeString = df.format((double) fileS / 1048576) + " MB";
            } else {
                fileSizeString = df.format((double) fileS / 1073741824) + " GB";
            }
            return fileSizeString;
        }


        @PostMapping("/file")
    public ActionResult file(@RequestBody JSONObject jsonObject) throws Exception {
        String taskId = jsonObject.getString("id");

        if (StringUtil.isEmpty(taskId)) {
            return ActionResult.fail("任务ID不能为空");
        }

        try {
            // 根据任务ID获取任务信息
            HzProjectTaskEntity taskEntity = hzProjectTaskService.getInfo(taskId);
            if (taskEntity == null) {
                return ActionResult.fail("任务信息不存在");
            }

            // 根据任务编号获取该任务下的成果列表
            List<HzProjectFruitEntity> fruitList = hzProjectFruitService.getListByTaskCode(taskEntity.getTaskCode());

            // 根据项目编号获取项目信息
            QueryWrapper<HzProjectEnity> projectQuery = new QueryWrapper<>();
            projectQuery.eq("f_project_code", taskEntity.getProjectCode());
            HzProjectEnity projectEntity = hzProjectService.getOne(projectQuery);
            if (projectEntity == null) {
                return ActionResult.fail("项目信息不存在");
            }
            String projectFolder = projectEntity.getProjectName() + "+" + projectEntity.getProjectCode();
            QueryWrapper<HzFileEnity> hzFileQuery = new QueryWrapper<>();
            hzFileQuery.eq("f_file_name", projectFolder);
            HzFileEnity hzFileEntity = hzFileMapper.selectOne(hzFileQuery);
            if (hzFileEntity == null) {
                hzFileEntity = new HzFileEnity();
                hzFileEntity.setFileName(projectFolder);
                hzFileEntity.setFilePath(projectFolder+"/");
                hzFileEntity.setLevel(1);
                hzFileEntity.setPid("0");
                hzFileEntity.setType(0);
                hzFileEntity.setProjectId(projectEntity.getId());
                hzFileMapper.insert(hzFileEntity);
            }
            String taskFolder = taskEntity.getCustomTaskName() + "+" + taskEntity.getTaskCode();
            QueryWrapper<HzFileEnity> hzFileQuery1 = new QueryWrapper<>();
            hzFileQuery1.eq("f_file_name", taskFolder);
           HzFileEnity hzFileEntity1 = hzFileMapper.selectOne(hzFileQuery1);
           if (hzFileEntity1 == null) {
               hzFileEntity1 = new HzFileEnity();
               hzFileEntity1.setFileName(taskFolder);
               hzFileEntity1.setFilePath(projectFolder+"/"+taskFolder+"/");
               hzFileEntity1.setLevel(2);
               hzFileEntity1.setPid(hzFileEntity.getId());
               hzFileEntity1.setProjectId(projectEntity.getId());
               hzFileEntity1.setTaskId(taskEntity.getId());
               hzFileEntity1.setType(0);
               hzFileEntity1.setUserId(taskEntity.getTaskUserId());
               hzFileMapper.insert(hzFileEntity1);
           }
            List<String> uploadedFiles = new ArrayList<>();

            // 遍历每个成果，处理其fileUrl字段中的文件
            for (HzProjectFruitEntity fruitEntity : fruitList) {
                String fileUrl = fruitEntity.getFileUrl();
                String fruitFolder = fruitEntity.getName() + "+" + fruitEntity.getFruitCode();
                QueryWrapper<HzFileEnity> hzFileQuery2 = new QueryWrapper<>();
                hzFileQuery2.eq("f_file_name", fruitFolder);
                HzFileEnity hzFileEntity2 = hzFileMapper.selectOne(hzFileQuery2);
                if (hzFileEntity2 == null) {
                    hzFileEntity2 = new HzFileEnity();
                    hzFileEntity2.setFilePath(projectFolder + "/" + taskFolder + "/" + fruitFolder + "/");
                    hzFileEntity2.setFileName(fruitFolder);
                    hzFileEntity2.setLevel(3);
                    hzFileEntity2.setPid(hzFileEntity1.getId());
                    hzFileEntity2.setType(0);
                    hzFileEntity2.setProjectId(hzFileEntity.getProjectId());
                    hzFileEntity2.setTaskId(hzFileEntity.getTaskId());
                    hzFileEntity2.setResultId(fruitEntity.getId());
                    hzFileMapper.insert(hzFileEntity2);
                }
                if (StringUtil.isEmpty(fileUrl)) {
                    continue; // 跳过没有文件的成果
                }
                // 构建Minio存储路径：项目名称+项目编号/任务名称+任务编号/成果名称+成果编号/
                String basePath = projectFolder + "/" + taskFolder + "/" + fruitFolder + "/";
                // 从fileUrl中提取文件名
                QueryWrapper<HzFileEnity> hzFileEntityFileDelete = new QueryWrapper<>();
                hzFileEntityFileDelete.eq("f_result_id", fruitEntity.getId());
                hzFileEntityFileDelete.eq("f_level", 4);
                hzFileMapper.delete(hzFileEntityFileDelete);
                List<UpFile> fileList = JsonUtil.getJsonToList(fileUrl, UpFile.class);
                for (UpFile file : fileList) {
                    String fileName = file.getName();
                    if (StringUtil.isEmpty(fileName)) {
                        fileName = "file_" + System.currentTimeMillis();
                    }
                    String objectName = basePath + fileName;
                    // 只更新hz_file表记录，不上传文件到Minio
                    try {
                        // 在hz_file表中新增记录
                        HzFileEnity hzFileEntityFile = new HzFileEnity();
                        hzFileEntityFile.setFileName(fileName);
                        hzFileEntityFile.setFilePath(objectName);
                        hzFileEntityFile.setLevel(4); // 文件层级：项目/任务/成果/文件
                        hzFileEntityFile.setPid(hzFileEntity2.getId()); // 暂时设为0，可根据需要调整
                        hzFileEntityFile.setTaskId(taskEntity.getId());
                        hzFileEntityFile.setUserId(taskEntity.getTaskUserId());
                        hzFileEntityFile.setProjectId(projectEntity.getId());
                        hzFileEntityFile.setResultId(fruitEntity.getId());
                        hzFileEntityFile.setType(1); // 1表示文件
                        hzFileEntityFile.setFileUrl(JSONObject.toJSONString(file));
                        hzFileMapper.insert(hzFileEntityFile);
                        uploadedFiles.add(fileName);
                    } catch (Exception e) {
                        log.error("处理成果文件失败: 成果ID={}, fileUrl={}, 错误={}", fruitEntity.getId(), fileUrl, e.getMessage());
                        // 继续处理下一个成果，不中断整个流程
                    }
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("processedFiles", uploadedFiles);
            result.put("processedFruits", fruitList.size());

            return ActionResult.success(result);

        } catch (Exception e) {
            log.error("文件处理失败: {}", e.getMessage(), e);
            return ActionResult.fail("文件处理失败: " + e.getMessage());
        }
    }

}
